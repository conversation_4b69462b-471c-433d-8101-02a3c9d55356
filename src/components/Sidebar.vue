<template>
  <Sidebar collapsible="icon">
    <SidebarHeader>
      <div class="flex justify-center items-center">
        <h1 class="app-title">星图</h1>
      </div>
    </SidebarHeader>

    <SidebarContent>
      <SidebarGroup>
        <SidebarGroupLabel>资源库</SidebarGroupLabel>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton :class="isHomeActive ? 'bg-primary/10' : ''" :as="routerLinkComponent" to="/resources">
              <Home class="w-4 h-4" />
              <span>首页</span>
            </SidebarMenuButton>
          </SidebarMenuItem>

          <!-- <SidebarMenuItem>
            <SidebarMenuButton :class="isMenuItemActive('/trash') ? 'bg-primary/10' : ''" :as="routerLinkComponent"
              to="/trash">
              <Trash2 class="w-4 h-4" />
              <span>回收站</span>
            </SidebarMenuButton>
          </SidebarMenuItem> -->
        </SidebarMenu>
      </SidebarGroup>

      <SidebarGroup>
        <SidebarGroupLabel>
          <span>文件夹</span>
        </SidebarGroupLabel>

        <SidebarMenu>
          <!-- 正常数据 -->
          <SidebarMenuItem v-for="folder in sidebarItems" :key="folder.id">
            <SidebarMenuButton :class="isMenuItemActive(folder.category_key) ? 'bg-primary/10' : ''"
              :as="routerLinkComponent"
              :to="`/resources/${folder.category_key}?categoryId=${folder.category_id}&parentId=${folder.id}`">
              <component :is="folder.icon" class="w-4 h-4" />
              <span>{{ folder.name }}</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarGroup>
    </SidebarContent>
  </Sidebar>
</template>

<script setup lang="ts">
import { computed, markRaw } from 'vue'
import { RouterLink, useRoute } from 'vue-router'
// import { Home, Trash2 } from 'lucide-vue-next'
import { Home } from 'lucide-vue-next'
import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from '@/components/ui/sidebar'
import { useSidebarStore } from '@/store/sidebar'
import { storeToRefs } from 'pinia'

// 使用 markRaw 避免 RouterLink 成为响应式对象
const routerLinkComponent = markRaw(RouterLink)

const route = useRoute()

const sidebarStore = useSidebarStore()
const { sidebarItems } = storeToRefs(sidebarStore)

// 检查菜单项是否激活
const isMenuItemActive = (categoryKey: string) => {
  const folderType = route.params.folderType as string
  return folderType === categoryKey
}

const isHomeActive = computed(() => {
  return route.path === '/resources'
})
</script>